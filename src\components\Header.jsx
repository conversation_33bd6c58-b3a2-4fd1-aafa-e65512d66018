import React from 'react';
import {
  AppB<PERSON>,
  Too<PERSON>bar,
  Typography,
  InputBase,
  IconButton,
  Badge,
  Avatar,
  Box,
  alpha
} from '@mui/material';
import {
  Search,
  Notifications,
  AccountCircle
} from '@mui/icons-material';

const Header = () => {
  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        backgroundColor: '#fff',
        color: '#333',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        borderBottom: '1px solid #e0e0e0'
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        {/* Left side - Title and breadcrumb */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#333' }}>
            Overview
          </Typography>
          <Typography variant="body2" sx={{ ml: 2, color: '#666' }}>
            Today
          </Typography>
        </Box>

        {/* Right side - Search, Notifications, Profile */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Search */}
          <Box
            sx={{
              position: 'relative',
              borderRadius: 1,
              backgroundColor: alpha('#000', 0.05),
              '&:hover': {
                backgroundColor: alpha('#000', 0.08),
              },
              marginLeft: 0,
              width: '100%',
              maxWidth: 300,
            }}
          >
            <Box
              sx={{
                padding: (theme) => theme.spacing(0, 2),
                height: '100%',
                position: 'absolute',
                pointerEvents: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Search sx={{ color: '#666' }} />
            </Box>
            <InputBase
              placeholder="Search"
              sx={{
                color: 'inherit',
                '& .MuiInputBase-input': {
                  padding: (theme) => theme.spacing(1, 1, 1, 0),
                  paddingLeft: `calc(1em + ${40}px)`,
                  transition: (theme) => theme.transitions.create('width'),
                  width: '100%',
                  fontSize: '0.875rem'
                },
              }}
            />
          </Box>

          {/* Notifications */}
          <Typography variant="body2" sx={{ color: '#666' }}>
            Notifications
          </Typography>
          <IconButton
            size="large"
            color="inherit"
            sx={{ color: '#666' }}
          >
            <Badge badgeContent={4} color="error">
              <Notifications />
            </Badge>
          </IconButton>

          {/* Profile */}
          <Avatar
            sx={{
              width: 32,
              height: 32,
              backgroundColor: '#1976d2',
              fontSize: '0.875rem'
            }}
          >
            U
          </Avatar>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
