import React from "react";
import {
  <PERSON>,
  Drawer,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Typography,
  Divider,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Grid,
  Paper,
  Card,
  CardContent,
  IconButton,
  Avatar,
  Badge,
  TextField,
  InputAdornment,
  ThemeProvider,
  createTheme,
  CssBaseline,
} from "@mui/material";
import {
  Dashboard,
  ExpandMore,
  ExpandLess,
  FolderOpen,
  Description,
  Notifications,
  Search,
  TrendingUp,
  TrendingDown,
  Remove,
  LightMode,
  DarkMode,
} from "@mui/icons-material";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

const drawerWidth = 240;

// Mock data
const stats = [
  { title: "Views", value: "7,265", change: "+11.02%", trend: "up" },
  { title: "Visits", value: "3,671", change: "-0.03%", trend: "down" },
  { title: "New Users", value: "156", change: "+15.03%", trend: "up" },
  { title: "Active Users", value: "2,318", change: "-6.08%", trend: "down" },
];

const marketingData = [
  { month: "Jan", value: 65 },
  { month: "Feb", value: 75 },
  { month: "Mar", value: 85 },
  { month: "Apr", value: 45 },
  { month: "May", value: 95 },
  { month: "Jun", value: 88 },
  { month: "Jul", value: 92 },
  { month: "Aug", value: 78 },
  { month: "Sep", value: 85 },
  { month: "Oct", value: 68 },
  { month: "Nov", value: 82 },
  { month: "Dec", value: 90 },
];

const totalUsersData = [
  { name: "Jan", totalVisitors: 4000, returningVisitors: 2400 },
  { name: "Feb", totalVisitors: 3000, returningVisitors: 1398 },
  { name: "Mar", totalVisitors: 2000, returningVisitors: 9800 },
  { name: "Apr", totalVisitors: 2780, returningVisitors: 3908 },
  { name: "May", totalVisitors: 1890, returningVisitors: 4800 },
  { name: "Jun", totalVisitors: 2390, returningVisitors: 3800 },
  { name: "Jul", totalVisitors: 3490, returningVisitors: 4300 },
];

const trafficByDeviceData = [
  { name: "Desktop", value: 45, color: "#1976d2" },
  { name: "Mobile", value: 35, color: "#42a5f5" },
  { name: "Tablet", value: 20, color: "#90caf9" },
];

const trafficByLocationData = [
  { name: "United States", value: 40, color: "#1976d2" },
  { name: "Canada", value: 25, color: "#42a5f5" },
  { name: "Mexico", value: 20, color: "#90caf9" },
  { name: "Other", value: 15, color: "#e3f2fd" },
];

function App() {
  const [expandedItems, setExpandedItems] = React.useState({});
  const [darkMode, setDarkMode] = React.useState(false);

  const handleExpand = (item) => {
    setExpandedItems((prev) => ({
      ...prev,
      [item]: !prev[item],
    }));
  };

  const toggleDarkMode = () => {
    setDarkMode((prev) => !prev);
  };

  // Create theme based on dark mode state
  const theme = createTheme({
    palette: {
      mode: darkMode ? "dark" : "light",
      primary: {
        main: "#1976d2",
      },
      secondary: {
        main: "#42a5f5",
      },
      background: {
        default: darkMode ? "#121212" : "#f5f5f5",
        paper: darkMode ? "#1e1e1e" : "#ffffff",
      },
    },
    components: {
      MuiDrawer: {
        styleOverrides: {
          paper: {
            backgroundColor: darkMode ? "#1e1e1e" : "#f8f9fa",
          },
        },
      },
    },
  });

  const StatCard = ({ title, value, change, trend }) => (
    <Card sx={{ height: "100%" }}>
      <CardContent>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-start",
          }}
        >
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography
              variant="h4"
              component="div"
              sx={{ fontWeight: "bold", mb: 1 }}
            >
              {value}
            </Typography>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              {trend === "up" ? (
                <TrendingUp
                  sx={{ color: "success.main", fontSize: 16, mr: 0.5 }}
                />
              ) : trend === "down" ? (
                <TrendingDown
                  sx={{ color: "error.main", fontSize: 16, mr: 0.5 }}
                />
              ) : (
                <Remove
                  sx={{ color: "text.secondary", fontSize: 16, mr: 0.5 }}
                />
              )}
              <Typography
                variant="body2"
                sx={{
                  color:
                    trend === "up"
                      ? "success.main"
                      : trend === "down"
                      ? "error.main"
                      : "text.secondary",
                  fontWeight: "medium",
                }}
              >
                {change}
              </Typography>
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  const TotalUsersChart = () => (
    <ResponsiveContainer width="100%" height={200}>
      <AreaChart data={totalUsersData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Area
          type="monotone"
          dataKey="totalVisitors"
          stackId="1"
          stroke="#1976d2"
          fill="#1976d2"
          fillOpacity={0.6}
        />
        <Area
          type="monotone"
          dataKey="returningVisitors"
          stackId="1"
          stroke="#42a5f5"
          fill="#42a5f5"
          fillOpacity={0.6}
        />
      </AreaChart>
    </ResponsiveContainer>
  );

  const MarketingBarChart = ({ data }) => (
    <ResponsiveContainer width="100%" height={200}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" />
        <YAxis />
        <Tooltip />
        <Bar dataKey="value" fill="#1976d2" />
      </BarChart>
    </ResponsiveContainer>
  );

  const TrafficPieChart = ({ data }) => (
    <Box sx={{ display: "flex", alignItems: "center", gap: 2, p: 2 }}>
      <ResponsiveContainer width={150} height={150}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={40}
            outerRadius={60}
            paddingAngle={5}
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
      <Box>
        {data.map((item, index) => (
          <Box
            key={index}
            sx={{ display: "flex", alignItems: "center", mb: 1 }}
          >
            <Box
              sx={{
                width: 12,
                height: 12,
                backgroundColor: item.color,
                borderRadius: "50%",
                mr: 1,
              }}
            />
            <Typography variant="body2" sx={{ mr: 1 }}>
              {item.name}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {item.value}%
            </Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: "flex" }}>
        {/* Sidebar */}
        <Drawer
          variant="permanent"
          sx={{
            width: drawerWidth,
            flexShrink: 0,
            "& .MuiDrawer-paper": {
              width: drawerWidth,
              boxSizing: "border-box",
              backgroundColor: darkMode ? "#1e1e1e" : "#f8f9fa",
              borderRight: darkMode ? "1px solid #333" : "1px solid #e0e0e0",
            },
          }}
        >
          <Toolbar>
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{ fontWeight: "bold" }}
            >
              SynUI.org
            </Typography>
          </Toolbar>
          <Divider />
          <List>
            {/* Overview */}
            <ListItem disablePadding>
              <ListItemButton onClick={() => handleExpand("overview")}>
                <ListItemIcon>
                  <Dashboard />
                </ListItemIcon>
                <ListItemText primary="Overview" />
                {expandedItems.overview ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
            </ListItem>

            {/* Projects */}
            <ListItem disablePadding>
              <ListItemButton onClick={() => handleExpand("projects")}>
                <ListItemIcon>
                  <FolderOpen />
                </ListItemIcon>
                <ListItemText primary="Projects" />
                {expandedItems.projects ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
            </ListItem>

            {/* Documents */}
            <ListItem disablePadding>
              <ListItemButton onClick={() => handleExpand("documents")}>
                <ListItemIcon>
                  <Description />
                </ListItemIcon>
                <ListItemText primary="Documents" />
                {expandedItems.documents ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
            </ListItem>

            {/* Content sections */}
            {["Content", "Content", "Content", "Content", "Content"].map(
              (text, index) => (
                <ListItem key={index} disablePadding>
                  <ListItemButton
                    onClick={() => handleExpand(`content${index}`)}
                  >
                    <ListItemIcon>
                      <Description />
                    </ListItemIcon>
                    <ListItemText primary={text} />
                    {expandedItems[`content${index}`] ? (
                      <ExpandLess />
                    ) : (
                      <ExpandMore />
                    )}
                  </ListItemButton>
                </ListItem>
              )
            )}
          </List>
        </Drawer>

        {/* Main content */}
        <Box
          component="main"
          sx={{ flexGrow: 1, bgcolor: "background.default" }}
        >
          {/* Top bar */}
          <AppBar
            position="static"
            color="transparent"
            elevation={0}
            sx={{ borderBottom: "1px solid #e0e0e0" }}
          >
            <Toolbar>
              <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                Test Test Test Test
              </Typography>
              <TextField
                placeholder="Search"
                size="small"
                sx={{ mr: 2, width: 200 }}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  },
                }}
              />
              <Typography variant="body2" sx={{ mr: 2 }}>
                Test Test Test Test
              </Typography>
              <IconButton onClick={toggleDarkMode} sx={{ mr: 1 }}>
                {darkMode ? <LightMode /> : <DarkMode />}
              </IconButton>
              <IconButton>
                <Badge badgeContent={4} color="error">
                  <Notifications />
                </Badge>
              </IconButton>
              <Avatar sx={{ ml: 1, bgcolor: "primary.main" }}>U</Avatar>
            </Toolbar>
          </AppBar>

          <Box sx={{ p: 3 }}>
            {/* Header */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h4" component="h1" gutterBottom>
                Overview
              </Typography>
              <Typography variant="body1" color="textSecondary">
                Today
              </Typography>
            </Box>

            {/* Stats Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {stats.map((stat, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <StatCard {...stat} />
                </Grid>
              ))}
            </Grid>

            {/* Charts Row */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {/* Total Users Chart */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, height: 300 }}>
                  <Typography variant="h6" gutterBottom>
                    Total Users
                  </Typography>
                  <Typography
                    variant="body2"
                    color="textSecondary"
                    gutterBottom
                  >
                    Total Visitors • Returning Visitors
                  </Typography>
                  <TotalUsersChart />
                </Paper>
              </Grid>

              {/* Traffic by Website */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, height: 300 }}>
                  <Typography variant="h6" gutterBottom>
                    Traffic by Website
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      gap: 1,
                      mt: 2,
                    }}
                  >
                    {[
                      "Google",
                      "Youtube",
                      "Instagram",
                      "Pinterest",
                      "Facebook",
                      "Twitter",
                    ].map((platform, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <Typography variant="body2">{platform}</Typography>
                        <Typography variant="body2">—</Typography>
                      </Box>
                    ))}
                  </Box>
                </Paper>
              </Grid>
            </Grid>

            {/* Bottom Charts Row */}
            <Grid container spacing={3}>
              {/* Traffic by Device */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, height: 300 }}>
                  <Typography variant="h6" gutterBottom>
                    Traffic by Device
                  </Typography>
                  <TrafficPieChart data={trafficByDeviceData} />
                </Paper>
              </Grid>

              {/* Traffic by Location */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, height: 300 }}>
                  <Typography variant="h6" gutterBottom>
                    Traffic by Location
                  </Typography>
                  <TrafficPieChart data={trafficByLocationData} />
                </Paper>
              </Grid>
            </Grid>

            {/* Marketing & SEO Chart */}
            <Grid container spacing={3} sx={{ mt: 2 }}>
              <Grid item xs={12}>
                <Paper sx={{ p: 2, height: 300 }}>
                  <Typography variant="h6" gutterBottom>
                    Marketing & SEO
                  </Typography>
                  <MarketingBarChart data={marketingData} />
                </Paper>
              </Grid>
            </Grid>
          </Box>
        </Box>

        {/* Right sidebar */}
        <Box
          sx={{
            width: 280,
            bgcolor: darkMode ? "#1e1e1e" : "#f8f9fa",
            p: 2,
            borderLeft: darkMode ? "1px solid #333" : "1px solid #e0e0e0",
          }}
        >
          <Typography variant="h6" gutterBottom>
            Activities
          </Typography>
          <List>
            {[
              "Test",
              "Test",
              "Test",
              "Test",
              "Test",
              "Test",
              "Test",
              "Test",
              "Test",
              "Test",
            ].map((item, index) => (
              <ListItem key={index} sx={{ py: 0.5 }}>
                <ListItemText
                  primary={<Typography variant="body2">{item}</Typography>}
                />
              </ListItem>
            ))}
          </List>

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Contacts
          </Typography>
          <List>
            {[
              "DMIN Corp",
              "Data Corp",
              "Budi Joko",
              "Panji Samudra",
              "Eko Murtono",
              "Nabilah Mira",
            ].map((contact, index) => (
              <ListItem key={index} sx={{ py: 0.5 }}>
                <ListItemText
                  primary={<Typography variant="body2">{contact}</Typography>}
                />
              </ListItem>
            ))}
          </List>
        </Box>
      </Box>
    </ThemeProvider>
  );
}

export default App;
