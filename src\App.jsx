import {
  Box,
  CssBaseline,
  Grid,
  Container,
  ThemeProvider,
  createTheme,
} from "@mui/material";
import Sidebar from "./components/Sidebar";
import Header from "./components/Header";
import StatsCards from "./components/StatsCards";
import TrafficChart from "./components/TrafficChart";
import TrafficByDevice from "./components/TrafficByDevice";
import TrafficByLocation from "./components/TrafficByLocation";
import MarketingSEO from "./components/MarketingSEO";
import ContactsList from "./components/ContactsList";

const theme = createTheme({
  palette: {
    background: {
      default: "#f8f9fa",
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: "flex" }}>
        {/* Sidebar */}
        <Sidebar />

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            bgcolor: "background.default",
            minHeight: "100vh",
          }}
        >
          {/* Header */}
          <Header />

          {/* Content */}
          <Container maxWidth="xl" sx={{ mt: 10, mb: 4 }}>
            <Grid container spacing={3}>
              {/* Stats Cards */}
              <Grid item xs={12}>
                <StatsCards />
              </Grid>

              {/* Charts Row */}
              <Grid item xs={12} lg={8}>
                <Grid container spacing={3}>
                  {/* Traffic Chart */}
                  <Grid item xs={12}>
                    <TrafficChart />
                  </Grid>

                  {/* Traffic by Device and Location */}
                  <Grid item xs={12} md={6}>
                    <TrafficByDevice />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TrafficByLocation />
                  </Grid>

                  {/* Marketing & SEO */}
                  <Grid item xs={12}>
                    <MarketingSEO />
                  </Grid>
                </Grid>
              </Grid>

              {/* Contacts Sidebar */}
              <Grid item xs={12} lg={4}>
                <ContactsList />
              </Grid>
            </Grid>
          </Container>
        </Box>
      </Box>
    </ThemeProvider>
  );
}

export default App;
