import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  Divider
} from '@mui/material';
import {
  Circle,
  FiberManualRecord
} from '@mui/icons-material';
import { contactsData } from '../data/mockData';

const getStatusColor = (status) => {
  switch (status) {
    case 'online':
      return '#4caf50';
    case 'away':
      return '#ff9800';
    case 'offline':
      return '#9e9e9e';
    default:
      return '#9e9e9e';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 'online':
      return 'Online';
    case 'away':
      return 'Away';
    case 'offline':
      return 'Offline';
    default:
      return 'Unknown';
  }
};

const ContactsList = () => {
  return (
    <Card
      sx={{
        height: '100%',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        borderRadius: 2
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Typography variant="h6" sx={{ fontWeight: 600, color: '#333', mb: 3 }}>
          Contacts
        </Typography>

        {/* Contacts List */}
        <List sx={{ p: 0 }}>
          {contactsData.map((contact, index) => (
            <React.Fragment key={index}>
              <ListItem
                sx={{
                  px: 0,
                  py: 1.5,
                  '&:hover': {
                    backgroundColor: '#f5f5f5',
                    borderRadius: 1,
                    cursor: 'pointer'
                  }
                }}
              >
                <ListItemAvatar>
                  <Box sx={{ position: 'relative' }}>
                    <Avatar
                      sx={{
                        width: 40,
                        height: 40,
                        backgroundColor: '#1976d2',
                        fontSize: '0.875rem',
                        fontWeight: 600
                      }}
                    >
                      {contact.avatar}
                    </Avatar>
                    <FiberManualRecord
                      sx={{
                        position: 'absolute',
                        bottom: 2,
                        right: 2,
                        fontSize: 12,
                        color: getStatusColor(contact.status),
                        backgroundColor: '#fff',
                        borderRadius: '50%'
                      }}
                    />
                  </Box>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 500,
                        color: '#333',
                        fontSize: '0.875rem'
                      }}
                    >
                      {contact.name}
                    </Typography>
                  }
                  secondary={
                    <Chip
                      label={getStatusText(contact.status)}
                      size="small"
                      sx={{
                        height: 20,
                        fontSize: '0.7rem',
                        backgroundColor: getStatusColor(contact.status),
                        color: '#fff',
                        '& .MuiChip-label': {
                          px: 1
                        }
                      }}
                    />
                  }
                />
              </ListItem>
              {index < contactsData.length - 1 && (
                <Divider sx={{ my: 0.5 }} />
              )}
            </React.Fragment>
          ))}
        </List>

        {/* Activities Section */}
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#333', mb: 2 }}>
            Activities
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ width: 32, height: 32, backgroundColor: '#e3f2fd', color: '#1976d2' }}>
                DC
              </Avatar>
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 500, color: '#333', fontSize: '0.875rem' }}>
                  David Chen
                </Typography>
                <Typography variant="caption" sx={{ color: '#666' }}>
                  2 minutes ago
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ width: 32, height: 32, backgroundColor: '#e8f5e8', color: '#2e7d32' }}>
                EM
              </Avatar>
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 500, color: '#333', fontSize: '0.875rem' }}>
                  Emma Miller
                </Typography>
                <Typography variant="caption" sx={{ color: '#666' }}>
                  5 minutes ago
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ width: 32, height: 32, backgroundColor: '#fff3e0', color: '#f57c00' }}>
                RJ
              </Avatar>
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 500, color: '#333', fontSize: '0.875rem' }}>
                  Robert Johnson
                </Typography>
                <Typography variant="caption" sx={{ color: '#666' }}>
                  10 minutes ago
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ContactsList;
