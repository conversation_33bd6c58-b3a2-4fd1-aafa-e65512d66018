import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip
} from '@mui/material';
import {
  Visibility,
  Star,
  PersonAdd,
  People,
  TrendingUp
} from '@mui/icons-material';
import { statsData } from '../data/mockData';

const iconMap = {
  visibility: <Visibility />,
  star: <Star />,
  person_add: <PersonAdd />,
  people: <People />
};

const StatsCard = ({ title, value, change, changeType, icon }) => {
  return (
    <Card
      sx={{
        height: '100%',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        borderRadius: 2,
        '&:hover': {
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          transform: 'translateY(-2px)',
          transition: 'all 0.3s ease'
        }
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box
            sx={{
              backgroundColor: '#e3f2fd',
              borderRadius: 2,
              p: 1.5,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Box sx={{ color: '#1976d2', fontSize: 24 }}>
              {iconMap[icon]}
            </Box>
          </Box>
          <Chip
            icon={<TrendingUp sx={{ fontSize: 16 }} />}
            label={change}
            size="small"
            sx={{
              backgroundColor: changeType === 'increase' ? '#e8f5e8' : '#ffebee',
              color: changeType === 'increase' ? '#2e7d32' : '#d32f2f',
              fontWeight: 600,
              fontSize: '0.75rem',
              '& .MuiChip-icon': {
                color: changeType === 'increase' ? '#2e7d32' : '#d32f2f'
              }
            }}
          />
        </Box>
        
        <Typography
          variant="h4"
          sx={{
            fontWeight: 700,
            color: '#333',
            mb: 0.5,
            fontSize: '2rem'
          }}
        >
          {value}
        </Typography>
        
        <Typography
          variant="body2"
          sx={{
            color: '#666',
            fontWeight: 500,
            textTransform: 'uppercase',
            letterSpacing: 0.5
          }}
        >
          {title}
        </Typography>
      </CardContent>
    </Card>
  );
};

const StatsCards = () => {
  return (
    <Grid container spacing={3}>
      {statsData.map((stat, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <StatsCard {...stat} />
        </Grid>
      ))}
    </Grid>
  );
};

export default StatsCards;
