import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend
} from 'recharts';
import { trafficByLocationData } from '../data/mockData';

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300'];

const TrafficByLocation = () => {
  const data = [
    { name: 'United States', value: 23.5, color: '#8884d8' },
    { name: 'Canada', value: 18.2, color: '#82ca9d' },
    { name: 'Mexico', value: 15.8, color: '#ffc658' },
    { name: 'Other', value: 42.5, color: '#ff7300' }
  ];

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize={12}
        fontWeight={600}
      >
        {`${(percent * 100).toFixed(1)}%`}
      </text>
    );
  };

  return (
    <Card
      sx={{
        height: '100%',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        borderRadius: 2
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Typography variant="h6" sx={{ fontWeight: 600, color: '#333', mb: 3 }}>
          Traffic by Location
        </Typography>

        {/* Chart */}
        <Box sx={{ height: 250, display: 'flex', justifyContent: 'center' }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: '#fff',
                  border: '1px solid #e0e0e0',
                  borderRadius: 8,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                formatter={(value) => [`${value}%`, 'Traffic']}
              />
            </PieChart>
          </ResponsiveContainer>
        </Box>

        {/* Legend */}
        <Box sx={{ mt: 2 }}>
          {data.map((item, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                py: 1,
                borderBottom: index < data.length - 1 ? '1px solid #f0f0f0' : 'none'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    backgroundColor: item.color,
                    borderRadius: '50%'
                  }}
                />
                <Typography variant="body2" sx={{ color: '#333', fontWeight: 500 }}>
                  {item.name}
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ color: '#666', fontWeight: 600 }}>
                {item.value}%
              </Typography>
            </Box>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
};

export default TrafficByLocation;
