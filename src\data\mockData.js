// Mock data for dashboard

export const statsData = [
  {
    title: "Visits",
    value: "7,265",
    change: "+11.01%",
    changeType: "increase",
    icon: "visibility"
  },
  {
    title: "VIP",
    value: "3,671",
    change: "+6.08%",
    changeType: "increase",
    icon: "star"
  },
  {
    title: "New Users",
    value: "156",
    change: "+15.03%",
    changeType: "increase",
    icon: "person_add"
  },
  {
    title: "Active Users",
    value: "2,318",
    change: "+6.08%",
    changeType: "increase",
    icon: "people"
  }
];

export const trafficChartData = [
  { name: 'Jan', users: 400 },
  { name: 'Feb', users: 300 },
  { name: 'Mar', users: 600 },
  { name: 'Apr', users: 800 },
  { name: 'May', users: 500 },
  { name: 'Jun', users: 700 },
  { name: 'Jul', users: 900 },
  { name: 'Aug', users: 600 },
  { name: 'Sep', users: 800 },
  { name: 'Oct', users: 700 },
  { name: 'Nov', users: 900 },
  { name: 'Dec', users: 1000 }
];

export const trafficByDeviceData = [
  { name: 'Desktop', value: 65, color: '#8884d8' },
  { name: 'Mobile', value: 25, color: '#82ca9d' },
  { name: 'Tablet', value: 10, color: '#ffc658' }
];

export const trafficByLocationData = [
  { name: 'United States', value: 23.5, color: '#8884d8' },
  { name: 'Canada', value: 18.2, color: '#82ca9d' },
  { name: 'Mexico', value: 15.8, color: '#ffc658' },
  { name: 'Other', value: 42.5, color: '#ff7300' }
];

export const marketingSEOData = [
  { name: 'Jan', value: 80 },
  { name: 'Feb', value: 85 },
  { name: 'Mar', value: 90 },
  { name: 'Apr', value: 75 },
  { name: 'May', value: 95 },
  { name: 'Jun', value: 88 },
  { name: 'Jul', value: 92 },
  { name: 'Aug', value: 87 },
  { name: 'Sep', value: 93 },
  { name: 'Oct', value: 89 },
  { name: 'Nov', value: 96 },
  { name: 'Dec', value: 91 }
];

export const contactsData = [
  { name: "John Doe", status: "online", avatar: "JD" },
  { name: "Jane Smith", status: "offline", avatar: "JS" },
  { name: "Mike Johnson", status: "online", avatar: "MJ" },
  { name: "Sarah Wilson", status: "away", avatar: "SW" },
  { name: "David Brown", status: "online", avatar: "DB" },
  { name: "Lisa Davis", status: "offline", avatar: "LD" },
  { name: "Tom Wilson", status: "online", avatar: "TW" },
  { name: "Emma Taylor", status: "away", avatar: "ET" },
  { name: "Alex Chen", status: "online", avatar: "AC" },
  { name: "Maria Garcia", status: "offline", avatar: "MG" }
];

export const sidebarMenuItems = [
  { text: "Overview", icon: "dashboard", active: true },
  { text: "Projects", icon: "folder" },
  { text: "Campaigns", icon: "campaign" },
  { text: "Content", icon: "article" },
  { text: "Content", icon: "content_copy" },
  { text: "Content", icon: "description" },
  { text: "Content", icon: "note" },
  { text: "Content", icon: "edit" },
  { text: "Content", icon: "create" },
  { text: "Content", icon: "library_books" },
  { text: "Content", icon: "bookmark" }
];

export const trafficWebsiteData = [
  { source: "Google", visitors: "1,234", percentage: 45.2 },
  { source: "YouTube", visitors: "987", percentage: 32.1 },
  { source: "Instagram", visitors: "654", percentage: 18.7 },
  { source: "Facebook", visitors: "321", percentage: 12.3 },
  { source: "Twitter", visitors: "198", percentage: 8.9 }
];
