import React from "react";
import { Card, CardContent, Typography, Box } from "@mui/material";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { marketingSEOData } from "../data/mockData";

const MarketingSEO = () => {
  const data = [
    { name: "Jan", value: 80, color: "#333" },
    { name: "Feb", value: 85, color: "#333" },
    { name: "Mar", value: 90, color: "#333" },
    { name: "Apr", value: 75, color: "#8884d8" },
    { name: "May", value: 95, color: "#333" },
    { name: "<PERSON>", value: 88, color: "#333" },
    { name: "Jul", value: 92, color: "#333" },
    { name: "Aug", value: 87, color: "#333" },
    { name: "Sep", value: 93, color: "#333" },
    { name: "Oct", value: 89, color: "#8884d8" },
    { name: "Nov", value: 96, color: "#333" },
    { name: "Dec", value: 91, color: "#333" },
  ];

  return (
    <Card
      sx={{
        height: "100%",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        borderRadius: 2,
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Typography variant="h6" sx={{ fontWeight: 600, color: "#333", mb: 3 }}>
          Marketing & SEO
        </Typography>

        {/* Chart */}
        <Box sx={{ height: 300 }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="name"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: "#666" }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: "#666" }}
                domain={[0, 100]}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#fff",
                  border: "1px solid #e0e0e0",
                  borderRadius: 8,
                  boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                }}
                formatter={(value) => [`${value}%`, "Performance"]}
              />
              <Bar dataKey="value" radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </Box>

        {/* Stats */}
        <Box sx={{ mt: 3, display: "flex", justifyContent: "space-between" }}>
          <Box sx={{ textAlign: "center" }}>
            <Typography variant="h6" sx={{ fontWeight: 700, color: "#333" }}>
              89%
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: "#666", fontSize: "0.75rem" }}
            >
              Average
            </Typography>
          </Box>
          <Box sx={{ textAlign: "center" }}>
            <Typography variant="h6" sx={{ fontWeight: 700, color: "#333" }}>
              96%
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: "#666", fontSize: "0.75rem" }}
            >
              Best
            </Typography>
          </Box>
          <Box sx={{ textAlign: "center" }}>
            <Typography variant="h6" sx={{ fontWeight: 700, color: "#333" }}>
              75%
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: "#666", fontSize: "0.75rem" }}
            >
              Lowest
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default MarketingSEO;
