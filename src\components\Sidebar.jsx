import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider
} from '@mui/material';
import {
  Dashboard,
  Folder,
  Campaign,
  Article,
  ContentCopy,
  Description,
  Note,
  Edit,
  Create,
  LibraryBooks,
  Bookmark
} from '@mui/icons-material';

const drawerWidth = 240;

const menuItems = [
  { text: "Overview", icon: <Dashboard />, active: true },
  { text: "Projects", icon: <Folder /> },
  { text: "Campaigns", icon: <Campaign /> },
  { text: "Content", icon: <Article /> },
  { text: "Content", icon: <ContentCopy /> },
  { text: "Content", icon: <Description /> },
  { text: "Content", icon: <Note /> },
  { text: "Content", icon: <Edit /> },
  { text: "Content", icon: <Create /> },
  { text: "Content", icon: <LibraryBooks /> },
  { text: "Content", icon: <Bookmark /> }
];

const Sidebar = () => {
  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          backgroundColor: '#f8f9fa',
          borderRight: '1px solid #e0e0e0'
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
          Dashboard
        </Typography>
      </Box>
      <Divider />
      <List sx={{ pt: 1 }}>
        {menuItems.map((item, index) => (
          <ListItem key={index} disablePadding>
            <ListItemButton
              sx={{
                mx: 1,
                borderRadius: 1,
                backgroundColor: item.active ? '#e3f2fd' : 'transparent',
                '&:hover': {
                  backgroundColor: item.active ? '#e3f2fd' : '#f5f5f5'
                }
              }}
            >
              <ListItemIcon
                sx={{
                  color: item.active ? '#1976d2' : '#666',
                  minWidth: 40
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.text}
                sx={{
                  '& .MuiListItemText-primary': {
                    fontSize: '0.875rem',
                    fontWeight: item.active ? 600 : 400,
                    color: item.active ? '#1976d2' : '#333'
                  }
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Drawer>
  );
};

export default Sidebar;
